import { useState } from 'react'
import { useNavigate } from 'react-router-dom'import { useAuth } from '../context/AuthContext'
import '../styles/pages/Login.css'
function Login() {  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)  const [error, setError] = useState('')
  const navigate = useNavigate()  const { login } = useAuth()
  const handleSubmit = async (e) => {
    e.preventDefault()    setError('')
        // Validate email
    if (!isValidEmail(email)) {      setError('Please enter a valid email address')
      return    }
        // Validate password (not empty)
    if (!password.trim()) {      setError('Password is required')
      return    }
        setIsLoading(true)
        try {
      // Call login function from auth context      await login(email, password, rememberMe)
            // Navigate to OTP verification page
      navigate('/verify-otp', {         state: { 
          email,          // Assuming phone number would be retrieved from user profile
          phone: '+**********' // This would come from your backend in a real app        } 
      })    } catch (err) {
      setError(err.message || 'Login failed. Please try again.')    } finally {
      setIsLoading(false)    }
  }  
  const isValidEmail = (email) => {    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }
  return (    <div className="login-container">
      <div className="login-card">        <div className="login-header">
          <h2>Fleet Management System</h2>          <p>Sign in to your account</p>
        </div>        
        {error && <div className="error-message">{error}</div>}        
        <form onSubmit={handleSubmit} className="login-form">          <div className="form-group">
            <label htmlFor="email">Email</label>            <input
              type="email"              id="email"
              value={email}              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"              required
            />          </div>
                    <div className="form-group">
            <label htmlFor="password">Password</label>            <input
              type="password"              id="password"
              value={password}              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"              required
            />          </div>
                    <div className="form-options">
            <div className="remember-me">              <input
                type="checkbox"                id="rememberMe"
                checked={rememberMe}                onChange={(e) => setRememberMe(e.target.checked)}
              />              <label htmlFor="rememberMe">Remember me</label>
            </div>            
            <a href="#" className="forgot-password">Forgot password?</a>          </div>
                    <button 
            type="submit"             className="login-button"
            disabled={isLoading}          >
            {isLoading ? 'Signing in...' : 'Sign In'}          </button>
        </form>      </div>
    </div>  )
}

export default Login



























































