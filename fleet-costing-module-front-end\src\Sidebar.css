/* Sidebar Component Styles */
.sidebar {
  width: 250px;
  background-color: #34495e;
  color: white;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  padding-top: 80px; /* Account for header height */
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 50;
  overflow-y: auto;
}

.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin: 0;
}

.sidebar-nav a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
  background-color: #2c3e50;
  color: white;
  border-left-color: #3498db;
}

.sidebar-nav a.active {
  background-color: #2c3e50;
  color: white;
  border-left-color: #3498db;
  font-weight: 500;
}

.sidebar-nav a.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #3498db;
}

/* Navigation icons (if you add them later) */
.nav-icon {
  margin-right: 0.75rem;
  width: 1.2rem;
  height: 1.2rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    padding-top: 0;
  }
  
  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
  }
  
  .sidebar-nav ul {
    display: flex;
    gap: 0.5rem;
    min-width: max-content;
  }
  
  .sidebar-nav a {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    border-left: none;
    white-space: nowrap;
  }
  
  .sidebar-nav a.active {
    background-color: #3498db;
    border-left: none;
  }
}

/* Scrollbar styling for webkit browsers */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #2c3e50;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
}
