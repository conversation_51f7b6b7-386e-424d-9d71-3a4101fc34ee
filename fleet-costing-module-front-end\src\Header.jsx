
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import './Header.css'

function Header() {
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate('/login', { replace: true })
  }

  return (
    <header className="header">
      <div className="header-left">
        <h1 className="header-title">
          <Link to="/">Fleet Management System</Link>
        </h1>
      </div>
      <div className="header-right">
        <div className="user-info">
          <span className="user-name">Welcome, {user?.name || 'User'}</span>
        </div>
        <button onClick={handleLogout} className="logout-btn">
          Logout
        </button>
      </div>
    </header>
  )
}

export default Header
