
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import './Header.css'

function Header() {
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate('/login', { replace: true })
  }

  return (
    <header className="header">
      <div className="logo">
        <Link to="/">Fleet Management System</Link>
      </div>
      <div className="user-menu">
        <span>Welcome, {user?.name || 'User'}</span>
        <button onClick={handleLogout} className="logout-btn">
          Logout
        </button>
      </div>
    </header>
  )
}

export default Header
