#root {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Proxima Nova', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

/* Loading styles for App component */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Proxima Nova', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.loading-spinner {
  text-align: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #666;
  font-size: 1rem;
  margin: 0;
  font-family: 'Proxima Nova', inherit;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Main application layout */
.app-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-container {
  display: flex;
  min-height: calc(100vh - 80px); /* Account for header height */
  margin-top: 80px; /* Account for fixed header */
}

.content {
  flex: 1;
  margin-left: 250px; /* Account for sidebar width */
  padding: 2rem;
  background-color: #ffffff;
  min-height: calc(100vh - 80px);
  overflow-y: auto;
}

/* Responsive layout */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    margin-top: 0;
  }

  .content {
    margin-left: 0;
    padding: 1rem;
  }
}
