# Authentication System Documentation

## Overview

This application implements a comprehensive authentication system with the following features:

- **Email/Password Login**: Users can log in with email and password
- **Two-Factor Authentication (2FA)**: OTP verification via email or SMS
- **Remember Me**: Persistent authentication across browser sessions
- **Protected Routes**: Automatic redirection for unauthenticated users
- **Token Management**: Secure token storage and validation
- **Error Handling**: Comprehensive error boundaries and user feedback

## Architecture

### Components

1. **AuthContext** (`src/context/AuthContext.jsx`)
   - Centralized authentication state management
   - Provides authentication methods to the entire app
   - <PERSON>les token validation and user session persistence

2. **ProtectedRoute** (`src/components/ProtectedRoute.jsx`)
   - Wrapper component for protected routes
   - Redirects unauthenticated users to login
   - Shows loading state during authentication checks

3. **ErrorBoundary** (`src/components/ErrorBoundary.jsx`)
   - Catches and handles authentication errors gracefully
   - Provides fallback UI for error states

4. **AuthService** (`src/services/authService.js`)
   - Centralized API service for authentication calls
   - Ready for real backend integration

### Authentication Flow

1. **Login Process**:
   ```
   User enters credentials → Login validation → OTP sent → OTP verification → Authentication complete
   ```

2. **Session Persistence**:
   ```
   App loads → Check stored token → Validate with backend → Restore session or redirect to login
   ```

3. **Route Protection**:
   ```
   User navigates → Check authentication → Allow access or redirect to login
   ```

## Usage

### Basic Setup

The authentication system is already integrated into the app. The main components are wrapped with the necessary providers:

```jsx
// main.jsx
<ErrorBoundary>
  <BrowserRouter>
    <AuthProvider>
      <App />
    </AuthProvider>
  </BrowserRouter>
</ErrorBoundary>
```

### Using Authentication in Components

```jsx
import { useAuth } from '../context/AuthContext'

function MyComponent() {
  const { user, isAuthenticated, logout } = useAuth()
  
  if (!isAuthenticated) {
    return <div>Please log in</div>
  }
  
  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <button onClick={logout}>Logout</button>
    </div>
  )
}
```

### Protecting Routes

Routes are automatically protected in the App component. All main application routes require authentication.

### Authentication State

The AuthContext provides the following state and methods:

- `user`: Current user object (null if not authenticated)
- `isAuthenticated`: Boolean indicating authentication status
- `isLoading`: Boolean indicating if authentication check is in progress
- `login(email, password, rememberMe)`: Login method
- `verifyOtp(identifier, otp, method)`: OTP verification method
- `resendOtp(identifier, method)`: Resend OTP method
- `logout()`: Logout method

## Security Features

1. **Token Validation**: Tokens are validated on app load and before API calls
2. **Secure Storage**: Authentication data is stored in localStorage with proper cleanup
3. **Remember Me**: Optional persistent sessions
4. **Error Handling**: Graceful handling of authentication errors
5. **Route Protection**: Automatic redirection for unauthorized access

## Demo Mode

Currently, the system runs in demo mode with simulated API calls. To integrate with a real backend:

1. Update the `authService.js` file with real API endpoints
2. Replace the demo validation logic in `AuthContext.jsx`
3. Configure proper environment variables for API URLs

## Environment Variables

Create a `.env` file in the root directory:

```
REACT_APP_API_URL=http://localhost:3001/api
```

## Future Enhancements

1. **Token Refresh**: Automatic token refresh before expiration
2. **Password Reset**: Forgot password functionality
3. **Social Login**: Integration with OAuth providers
4. **Role-Based Access**: Different access levels for different user roles
5. **Session Management**: Multiple device session handling
6. **Security Headers**: CSRF protection and security headers

## Troubleshooting

### Common Issues

1. **Infinite Redirect Loop**: Check that login/OTP routes are not protected
2. **Token Not Persisting**: Verify localStorage is working and "Remember Me" is checked
3. **Authentication State Not Updating**: Ensure components are wrapped with AuthProvider

### Debug Mode

To enable debug logging, add this to your browser console:

```javascript
localStorage.setItem('debug', 'auth:*')
```

## Testing

The authentication system includes proper error handling and loading states. Test scenarios:

1. **Valid Login**: Enter any email/password, verify with any 4+ digit OTP
2. **Invalid Credentials**: Test error handling with empty fields
3. **Session Persistence**: Check "Remember Me" and refresh the page
4. **Route Protection**: Try accessing protected routes while logged out
5. **Logout**: Verify complete session cleanup

## API Integration

When ready to integrate with a real backend, update these files:

1. `src/services/authService.js` - Replace demo API calls
2. `src/context/AuthContext.jsx` - Remove demo logic, use real API responses
3. Environment configuration for API endpoints

The current structure is designed to make this transition seamless.
