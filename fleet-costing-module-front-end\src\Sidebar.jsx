import { NavLink } from 'react-router-dom'
import './Sidebar.css'

function Sidebar() {
  return (
    <aside className="sidebar">
      <nav className="sidebar-nav">
        <NavLink to="/" className={({ isActive }) => isActive ? 'active' : ''}>
          Dashboard
        </NavLink>
        <NavLink to="/allocation" className={({ isActive }) => isActive ? 'active' : ''}>
          Fleet Allocation
        </NavLink>
        <NavLink to="/maintenance" className={({ isActive }) => isActive ? 'active' : ''}>
          Fleet Maintenance
        </NavLink>
        <NavLink to="/reports" className={({ isActive }) => isActive ? 'active' : ''}>
          Reports
        </NavLink>
        <NavLink to="/admin" className={({ isActive }) => isActive ? 'active' : ''}>
          Administration
        </NavLink>
      </nav>
    </aside>
  )
}

export default Sidebar