import { NavLink } from 'react-router-dom'
import './Sidebar.css'

function Sidebar() {
  return (
    <aside className="sidebar">
      <nav className="sidebar-nav">
        <ul>
          <li>
            <NavLink to="/" className={({ isActive }) => isActive ? 'active' : ''}>
              Dashboard
            </NavLink>
          </li>
          <li>
            <NavLink to="/allocation" className={({ isActive }) => isActive ? 'active' : ''}>
              Fleet Allocation
            </NavLink>
          </li>
          <li>
            <NavLink to="/maintenance" className={({ isActive }) => isActive ? 'active' : ''}>
              Fleet Maintenance
            </NavLink>
          </li>
          <li>
            <NavLink to="/reports" className={({ isActive }) => isActive ? 'active' : ''}>
              Reports
            </NavLink>
          </li>
          <li>
            <NavLink to="/admin" className={({ isActive }) => isActive ? 'active' : ''}>
              Administration
            </NavLink>
          </li>
        </ul>
      </nav>
    </aside>
  )
}

export default Sidebar