/* Login Page Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  font-family: 'Proxima Nova', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 400px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-family: 'Proxima Nova', inherit;
}

.login-subtitle {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
  font-family: 'Proxima Nova', inherit;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
  font-family: 'Proxima Nova', inherit;
}

.form-input {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Proxima Nova', inherit;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-input {
  width: 1rem;
  height: 1rem;
  accent-color: #3498db;
}

.checkbox-label {
  font-size: 0.9rem;
  color: #2c3e50;
  cursor: pointer;
  font-family: 'Proxima Nova', inherit;
}

.login-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 0.875rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  font-family: 'Proxima Nova', inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.login-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #fdf2f2;
  color: #e74c3c;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
}

.success-message {
  background-color: #f2fdf2;
  color: #27ae60;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #c6f5cb;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .form-input,
  .login-button {
    padding: 1rem;
  }
}

/* Focus visible for accessibility */
.login-button:focus-visible,
.form-input:focus-visible,
.checkbox-input:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}
