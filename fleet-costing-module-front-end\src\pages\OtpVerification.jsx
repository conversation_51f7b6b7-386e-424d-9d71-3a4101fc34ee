import { useState, useEffect, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import '../styles/pages/OtpVerification.css'

function OtpVerification() {
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [timeLeft, setTimeLeft] = useState(0)
  const [deliveryMethod, setDeliveryMethod] = useState('')
  const [otpSent, setOtpSent] = useState(false)
  const otpInputRef = useRef(null)
  const navigate = useNavigate()
  const location = useLocation()
  const { verifyOtp, resendOtp } = useAuth()
  
  // Get data from location state
  const email = location.state?.email || ''
  const phone = location.state?.phone || ''
  const from = location.state?.from || '/'
  
  // Format identifiers for display
  const maskedEmail = formatEmail(email)
  const maskedPhone = formatPhone(phone)

  useEffect(() => {
    // If a delivery method is selected, start the timer
    if (deliveryMethod) {
      setTimeLeft(30)
      const timer = setInterval(() => {
        setTimeLeft(prev => (prev > 0 ? prev - 1 : 0))
      }, 1000)
      
      return () => clearInterval(timer)
    }
  }, [deliveryMethod])

  // Format email for display (mask email)
  function formatEmail(value) {
    if (!value) return ''
    
    const [username, domain] = value.split('@')
    if (!username || !domain) return value
    
    const maskedUsername = username.charAt(0) + 
      '*'.repeat(Math.max(1, username.length - 2)) + 
      username.charAt(username.length - 1)
    
    return `${maskedUsername}@${domain}`
  }
  
  // Format phone for display (mask phone)
  function formatPhone(value) {
    if (!value) return ''
    return value.slice(0, -4).replace(/./g, '*') + value.slice(-4)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!otp || otp.length < 4) {
      setError('Please enter a valid OTP code')
      return
    }
    
    setError('')
    setIsLoading(true)
    
    try {
      // Call verify OTP function from auth context
      const identifier = deliveryMethod === 'email' ? email : phone
      await verifyOtp(identifier, otp, deliveryMethod)

      // Navigate to intended destination or dashboard on success
      navigate(from, { replace: true })
    } catch (err) {
      setError('Invalid verification code')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendOtp = async (method) => {
    setDeliveryMethod(method)
    setError('')
    setOtpSent(true)
    
    try {
      // Call resend OTP function from auth context with the selected method
      const identifier = method === 'email' ? email : phone
      await resendOtp(identifier, method)
      
      // Focus the OTP input when OTP is sent
      if (otpInputRef.current) {
        otpInputRef.current.focus()
      }
    } catch (err) {
      setError(`Failed to send verification code to your ${method}`)
    }
  }

  const handleResendOtp = async () => {
    if (timeLeft > 0) return
    
    try {
      // Resend using current delivery method
      const identifier = deliveryMethod === 'email' ? email : phone
      await resendOtp(identifier, deliveryMethod)
      setTimeLeft(30)
    } catch (err) {
      setError('Failed to resend verification code')
    }
  }

  const handleGoBack = () => {
    navigate('/login')
  }

  // If no delivery method is selected yet, show the selection screen
  if (!otpSent) {
    return (
      <div className="otp-container">
        <div className="otp-card">
          <div className="otp-header">
            <h2>Verification</h2>
            <p className="select-method-text">Select where to send verification code</p>
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <div className="option-buttons">
            <button 
              className="option-button"
              onClick={() => handleSendOtp('email')}
              disabled={!email}
            >
              Send OTP via Email
            </button>
            
            <button 
              className="option-button"
              onClick={() => handleSendOtp('phone')}
              disabled={!phone}
            >
              Send OTP via SMS
            </button>
            
            <button 
              className="option-button back-button"
              onClick={handleGoBack}
            >
              GO BACK
            </button>
          </div>
        </div>
      </div>
    )
  }

  // If delivery method is selected, show the OTP input screen
  return (
    <div className="otp-container">
      <div className="otp-card">
        <div className="otp-header">
          <h2>Verification</h2>
          <p className="otp-instruction">
            We sent you an {deliveryMethod === 'email' ? 'email' : 'SMS'} with code to verify your login attempt.
          </p>
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit} className="otp-form">
          <div className="form-group">
            <label htmlFor="otp">Enter your OTP code here:</label>
            <input
              type="text"
              id="otp"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/[^0-9]/g, ''))}
              ref={otpInputRef}
              className="otp-input"
              placeholder="Enter OTP"
              maxLength={6}
              required
            />
          </div>
          
          <button 
            type="submit" 
            className="verify-button"
            disabled={isLoading}
          >
            {isLoading ? 'Verifying...' : 'Enter your OTP code here'}
          </button>
          
          <div className="resend-options">
            <p className="didnt-receive">I didn't receive a code</p>
            <button 
              type="button" 
              className="resend-button"
              disabled={timeLeft > 0}
              onClick={handleResendOtp}
            >
              {timeLeft > 0 ? `Resend code (${timeLeft}s)` : 'Resend code'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default OtpVerification