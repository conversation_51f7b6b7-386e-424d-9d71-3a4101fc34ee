// Authentication service for API calls
// This is a placeholder for real API integration

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api'

class AuthService {
  // Login with email and password
  async login(email, password) {
    // In a real app, this would make an HTTP request to your backend
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      throw new Error('Invalid credentials')
    }

    return response.json()
  }

  // Send OTP to user
  async sendOtp(identifier, method = 'email') {
    const response = await fetch(`${API_BASE_URL}/auth/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ identifier, method }),
    })

    if (!response.ok) {
      throw new Error(`Failed to send OTP to ${method}`)
    }

    return response.json()
  }

  // Verify OTP code
  async verifyOtp(identifier, otp, method = 'email') {
    const response = await fetch(`${API_BASE_URL}/auth/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ identifier, otp, method }),
    })

    if (!response.ok) {
      throw new Error('Invalid verification code')
    }

    return response.json()
  }

  // Validate existing token
  async validateToken(token) {
    const response = await fetch(`${API_BASE_URL}/auth/validate`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('Invalid token')
    }

    return response.json()
  }

  // Refresh token
  async refreshToken(refreshToken) {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    })

    if (!response.ok) {
      throw new Error('Failed to refresh token')
    }

    return response.json()
  }

  // Logout
  async logout(token) {
    try {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
    } catch (error) {
      // Logout should succeed even if the API call fails
      console.warn('Logout API call failed:', error)
    }
  }
}

// Export a singleton instance
export default new AuthService()

