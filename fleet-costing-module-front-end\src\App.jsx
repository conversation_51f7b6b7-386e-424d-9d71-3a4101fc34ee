import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import ProtectedRoute from './components/ProtectedRoute'
import Header from './Header.jsx'
import Sidebar from './Sidebar.jsx'
import Dashboard from './components/Dashboard.jsx'
import FleetAllocation from './components/FleetAllocation.jsx'
import FleetMaintenance from './components/FleetMaintenance.jsx'
import Reports from './components/Reports.jsx'
import Administration from './components/Administration.jsx'
import Login from './pages/login.jsx'
import OtpVerification from './pages/OtpVerification.jsx'
import './App.css'

function App() {
  const { isAuthenticated, isLoading } = useAuth()

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="app-container">
      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <Login />
          }
        />
        <Route
          path="/verify-otp"
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <OtpVerification />
          }
        />

        {/* Protected routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <Header />
              <div className="main-container">
                <Sidebar />
                <main className="content">
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/allocation" element={<FleetAllocation />} />
                    <Route path="/maintenance" element={<FleetMaintenance />} />
                    <Route path="/reports" element={<Reports />} />
                    <Route path="/admin" element={<Administration />} />
                    {/* Catch all route for protected area */}
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </main>
              </div>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

export default App

