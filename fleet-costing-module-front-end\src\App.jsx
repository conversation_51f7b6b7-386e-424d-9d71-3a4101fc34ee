import { Routes, Route } from 'react-router-dom'
import ErrorBoundary from './components/ErrorBoundary'
import ProtectedRoute from './components/ProtectedRoute'
import Login from './pages/Login'
import OtpVerification from './pages/OtpVerification'
import Dashboard from './pages/Dashboard'

function App() {
  return (
    <Routes>
      <Route 
        path="/login" 
        element={
          <ErrorBoundary>
            <Login />
          </ErrorBoundary>
        } 
      />
      <Route 
        path="/verify-otp" 
        element={
          <ErrorBoundary>
            <OtpVerification />
          </ErrorBoundary>
        } 
      />
      <Route 
        path="/*" 
        element={
          <ErrorBoundary>
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          </ErrorBoundary>
        } 
      />
    </Routes>
  )
}

export default App


