import { Routes, Route } from 'react-router-dom'
import Header from './Header.jsx'
import Sidebar from './Sidebar.jsx'
import Dashboard from './components/Dashboard.jsx'
import FleetAllocation from './components/FleetAllocation.jsx'
import FleetMaintenance from './components/FleetMaintenance.jsx'
import Reports from './components/Reports.jsx'
import Administration from './components/Administration.jsx'
import './App.css'

function App() {
  return (
    <div className="app-container">
      <Header />
      <div className="main-container">
        <Sidebar />
        <main className="content">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/allocation" element={<FleetAllocation />} />
            <Route path="/maintenance" element={<FleetMaintenance />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/admin" element={<Administration />} />
          </Routes>
        </main>
      </div>
    </div>
  )
}

export default App

