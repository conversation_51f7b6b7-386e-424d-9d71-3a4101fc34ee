.otp-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Proxima Nova', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.otp-card {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.otp-header {
  margin-bottom: 2rem;
}

.otp-header h2 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-size: 1.75rem;
  font-family: 'Proxima Nova', inherit;
}

.select-method-text {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-family: 'Proxima Nova', inherit;
}

.otp-instruction {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-family: 'Proxima Nova', inherit;
}

/* Vertical button layout with significant spacing */
.option-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem; /* Significant spacing between buttons */
  margin: 2rem 0;
}

.option-button {
  width: 100%;
  max-width: 280px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
  color: #2c3e50;
  font-weight: 500;
  font-size: 1rem;
  font-family: 'Proxima Nova', inherit;
  cursor: pointer;
  transition: all 0.2s;
}

.option-button:hover:not(:disabled) {
  border-color: #3498db;
  background-color: #f0f7fc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.option-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.back-button {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
}

.back-button:hover {
  background-color: #e0e0e0;
  border-color: #bdc3c7;
}

.otp-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.form-group label {
  font-weight: 500;
  color: #2c3e50;
  font-family: 'Proxima Nova', inherit;
}

.otp-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  font-family: 'Proxima Nova', inherit;
  text-align: center;
  letter-spacing: 0.5rem;
  font-weight: 600;
}

.otp-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.verify-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  font-family: 'Proxima Nova', inherit;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 0.5rem;
}

.verify-button:hover {
  background-color: #2980b9;
}

.verify-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.resend-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1rem;
}

.didnt-receive {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-family: 'Proxima Nova', inherit;
}

.resend-button {
  background: none;
  border: none;
  color: #3498db;
  font-weight: 500;
  font-family: 'Proxima Nova', inherit;
  cursor: pointer;
  padding: 0;
}

.resend-button:disabled {
  color: #95a5a6;
  cursor: not-allowed;
}

.resend-button:hover:not(:disabled) {
  text-decoration: underline;
}

.error-message {
  background-color: #fdeded;
  color: #ef5350;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-family: 'Proxima Nova', inherit;
  text-align: center;
}