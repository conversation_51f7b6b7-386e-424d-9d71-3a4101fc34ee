import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext(null)

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // Check if user is already logged in
    const checkAuthStatus = async () => {
      const token = localStorage.getItem('authToken')
      const rememberMe = localStorage.getItem('rememberMe') === 'true'
      
      if (token && rememberMe) {
        try {
          // In a real app, you would validate the token with your backend
          // const userData = await authService.validateToken(token)
          
          // For demo purposes, we'll just set a dummy user
          setUser({ 
            name: '<PERSON>', 
            email: '<EMAIL>', 
            phone: '+**********', 
            role: 'admin' 
          })
          setIsAuthenticated(true)
        } catch (error) {
          // Token is invalid, clear it
          localStorage.removeItem('authToken')
          localStorage.removeItem('rememberMe')
        }
      } else if (token && !rememberMe) {
        // Clear token if remember me was not checked
        localStorage.removeItem('authToken')
      }
      
      setIsLoading(false)
    }
    
    checkAuthStatus()
  }, [])

  const login = async (email, password, rememberMe = false) => {
    try {
      // In a real app, you would call your auth service
      // const response = await authService.login(email, password)
      
      // For demo purposes, we'll simulate an API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Store remember me preference
      localStorage.setItem('rememberMe', rememberMe)
      
      // Return success
      return true
    } catch (error) {
      throw new Error('Invalid credentials')
    }
  }

  const verifyOtp = async (identifier, otp, method = 'email') => {
    try {
      // In a real app, you would call your auth service
      // const response = await authService.verifyOtp(identifier, otp, method)
      
      // For demo purposes, we'll simulate an API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Set a dummy token and user
      const token = 'dummy-jwt-token'
      localStorage.setItem('authToken', token)
      
      // Create user object
      const userData = { 
        name: 'John Doe', 
        email: method === 'email' ? identifier : '<EMAIL>',
        phone: method === 'phone' ? identifier : '+**********',
        role: 'admin'
      }
      
      setUser(userData)
      setIsAuthenticated(true)
      
      return userData
    } catch (error) {
      throw new Error('Invalid verification code')
    }
  }

  const resendOtp = async (identifier, method = 'email') => {
    try {
      // In a real app, you would call your auth service
      // await authService.sendOtp(identifier, method)
      
      // For demo purposes, we'll simulate an API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      return true
    } catch (error) {
      throw new Error(`Failed to send verification code to ${method}`)
    }
  }

  const logout = () => {
    localStorage.removeItem('authToken')
    setUser(null)
    setIsAuthenticated(false)
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      isLoading, 
      isAuthenticated, 
      login, 
      verifyOtp,
      resendOtp, 
      logout 
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  return useContext(AuthContext)
}