import { useState, useEffect } from 'react'
import { AuthContext } from './AuthContextDefinition'

// Token management utilities
const TOKEN_KEY = 'authToken'
const REMEMBER_ME_KEY = 'rememberMe'
const USER_DATA_KEY = 'userData'

const getStoredToken = () => localStorage.getItem(TOKEN_KEY)
const getRememberMe = () => localStorage.getItem(REMEMBER_ME_KEY) === 'true'
const getStoredUserData = () => {
  try {
    const userData = localStorage.getItem(USER_DATA_KEY)
    return userData ? JSON.parse(userData) : null
  } catch (error) {
    console.warn('Failed to parse stored user data:', error)
    // Clear corrupted data
    localStorage.removeItem(USER_DATA_KEY)
    return null
  }
}

const setStoredAuth = (token, userData, rememberMe) => {
  localStorage.setItem(TOKEN_KEY, token)
  localStorage.setItem(USER_DATA_KEY, JSO<PERSON>.stringify(userData))
  localStorage.setItem(REMEMBER_ME_KEY, rememberMe.toString())
}

const clearStoredAuth = () => {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(USER_DATA_KEY)
  localStorage.removeItem(REMEMBER_ME_KEY)
}

// Simulate token validation (replace with real API call)
const validateToken = async (token) => {
  // In a real app, this would make an API call to validate the token
  await new Promise(resolve => setTimeout(resolve, 500))

  // For demo: consider token valid if it's not empty and not expired
  if (token === 'dummy-jwt-token') {
    return true
  }
  throw new Error('Invalid token')
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    let isMounted = true

    // Check if user is already logged in
    const checkAuthStatus = async () => {
      try {
        const token = getStoredToken()
        const rememberMe = getRememberMe()
        const storedUserData = getStoredUserData()

        if (token && rememberMe && storedUserData) {
          try {
            // Validate token with backend
            await validateToken(token)

            // Only update state if component is still mounted
            if (isMounted) {
              // Token is valid, restore user session
              setUser(storedUserData)
              setIsAuthenticated(true)
            }
          } catch (tokenError) {
            // Token is invalid, clear stored data
            clearStoredAuth()
            console.warn('Stored token is invalid, clearing auth data:', tokenError.message)
          }
        } else if (token && !rememberMe) {
          // Clear token if remember me was not checked
          clearStoredAuth()
        }
      } catch (error) {
        console.error('Error checking auth status:', error)
        clearStoredAuth()
      } finally {
        // Only update loading state if component is still mounted
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    checkAuthStatus()

    // Cleanup function to prevent state updates on unmounted component
    return () => {
      isMounted = false
    }
  }, []) // Should include validateToken and clearStoredAuth

  const login = async (email, password, rememberMe = false) => {
    try {
      // In a real app, you would call your auth service
      // const response = await authService.login(email, password)

      // For demo purposes, validate credentials
      if (!email || !password) {
        throw new Error('Email and password are required')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // For demo: accept any non-empty credentials
      // In real app, this would be handled by the backend

      // Store remember me preference temporarily (will be used in verifyOtp)
      localStorage.setItem(REMEMBER_ME_KEY, rememberMe.toString())

      // Return success - OTP verification will complete the login
      return { success: true, requiresOtp: true }
    } catch (error) {
      throw new Error(error.message || 'Invalid credentials')
    }
  }

  const verifyOtp = async (identifier, otp, method = 'email') => {
    try {
      // In a real app, you would call your auth service
      // const response = await authService.verifyOtp(identifier, otp, method)

      // Validate OTP input
      if (!otp || otp.length < 4) {
        throw new Error('Please enter a valid OTP code')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // For demo: accept any OTP with 4+ digits
      // In real app, this would be validated by the backend

      // Generate token and user data
      const token = 'dummy-jwt-token'
      const userData = {
        id: '1',
        name: 'John Doe',
        email: method === 'email' ? identifier : '<EMAIL>',
        phone: method === 'phone' ? identifier : '+1234567890',
        role: 'admin',
        loginTime: new Date().toISOString()
      }

      // Get remember me preference
      const rememberMe = getRememberMe()

      // Store authentication data
      setStoredAuth(token, userData, rememberMe)

      // Update state
      setUser(userData)
      setIsAuthenticated(true)

      return userData
    } catch (error) {
      throw new Error(error.message || 'Invalid verification code')
    }
  }

  const resendOtp = async (identifier, method = 'email') => {
    try {
      // In a real app, you would call your auth service
      // await authService.sendOtp(identifier, method)

      // Validate identifier
      if (!identifier) {
        throw new Error('Identifier is required')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      // For demo purposes, always succeed
      return { success: true, message: `OTP sent to ${method}` }
    } catch (error) {
      throw new Error(error.message || `Failed to send verification code to ${method}`)
    }
  }

  const logout = () => {
    // Clear all stored authentication data
    clearStoredAuth()

    // Update state
    setUser(null)
    setIsAuthenticated(false)
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      isLoading, 
      isAuthenticated, 
      login, 
      verifyOtp,
      resendOtp, 
      logout 
    }}>
      {children}
    </AuthContext.Provider>
  )
}


