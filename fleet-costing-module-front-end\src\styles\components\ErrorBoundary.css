.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
  font-family: 'Proxima Nova', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.error-content {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.error-content h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-family: 'Proxima Nova', inherit;
}

.error-content p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
  font-family: 'Proxima Nova', inherit;
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-family: 'Proxima Nova', inherit;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2980b9;
}

.retry-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}
